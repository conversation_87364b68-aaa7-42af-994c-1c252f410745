require 'rails_helper'

RSpec.describe Bot::Tools::ActivityTool, type: :service do
  let(:tool) { described_class.new }

  describe '#query_records' do
    context '时间范围查询' do
      it '应该处理最近一周的查询' do
        result = tool.query_records(query: "最近一周的素材")
        expect(result).to be_present
        expect(result).to have_key(:total_count)
      end

      it '应该处理最近一个月的查询' do
        result = tool.query_records(query: "最近一个月的素材")
        expect(result).to be_present
        expect(result).to have_key(:total_count)
      end
    end

    context '状态查询' do
      it '应该处理已发布状态查询' do
        result = tool.query_records(query: "已发布的素材")
        expect(result).to be_present
        expect(result).to have_key(:total_count)
      end

      it '应该处理待发布状态查询' do
        result = tool.query_records(query: "待发布的素材")
        expect(result).to be_present
        expect(result).to have_key(:total_count)
      end
    end

    context '组合查询' do
      it '应该处理时间和状态的组合查询' do
        result = tool.query_records(query: "最近一个月已发布的素材")
        expect(result).to be_present
        expect(result).to have_key(:total_count)
      end
    end

    context '来源查询' do
      it '应该处理来源查询' do
        result = tool.query_records(query: "微信公众号的素材")
        expect(result).to be_present
        expect(result).to have_key(:total_count)
      end
    end

    context '标签查询' do
      it '应该处理标签查询' do
        result = tool.query_records(query: "带有技术标签的素材")
        expect(result).to be_present
        expect(result).to have_key(:total_count)
      end
    end
  end

  describe '预定义方法' do
    it '#recent_activities 应该返回最近的素材' do
      result = tool.recent_activities(days: 7)
      expect(result).to be_present
      expect(result).to have_key(:total_count)
    end

    it '#published_activities 应该返回已发布的素材' do
      result = tool.published_activities
      expect(result).to be_present
      expect(result).to have_key(:total_count)
    end

    it '#pending_activities 应该返回待发布的素材' do
      result = tool.pending_activities
      expect(result).to be_present
      expect(result).to have_key(:total_count)
    end
  end
end
