# 素材查询功能完整流程文档

## 概述

本文档详细描述了素材查询功能的完整实现流程，从用户输入到前端组件渲染的每个步骤，包括涉及的关键文件、类、方法和数据流。

## 系统架构

素材查询功能基于Bot::Agent系统实现，采用Intent识别 → Tool调用 → Artifact创建 → 前端渲染的完整流程。

```
用户输入 → Bot::Agent → Intent识别 → RansackTool → Artifact创建 → 前端组件渲染
```

## 详细流程

### 1. 用户输入阶段

**触发条件**: 用户在聊天界面输入素材查询相关的自然语言
- 示例: "最近一个月的素材"、"已发布的素材"、"素材查询"

**处理入口**: 
- 前端: 聊天界面发送消息
- 后端: `Bot::User::ConversationsController#create_message`

### 2. Bot::Agent系统处理

**关键文件**: 
- `rails_bot/app/models/bot/agent.rb`
- `rails_bot/app/services/bot/assistant.rb`

**处理流程**:
1. 接收用户消息
2. 调用LLM进行Intent识别
3. 匹配到"素材查询"Intent (ID: 18)

**Intent配置**:
```ruby
# 数据库记录
{
  id: 18,
  name: "素材查询",
  description: "专门查询系统中的素材信息，支持时间范围、状态、关键词等条件查询",
  tool_cname: "Bot::Tools::RansackTool",
  tool_function: "query_records",
  tool_conf: {
    model_class: "Serve::Activity",
    scope_chain: "all"
  }
}
```

### 3. RansackTool工具调用

**关键文件**: 
- `rails_bot/app/services/bot/tools/ransack_tool.rb`

**处理流程**:
1. 接收用户查询文本
2. 使用LLM解析查询条件
3. 转换为Ransack查询参数
4. 执行数据库查询
5. 创建Bot::ActivityListArtifact

**查询条件解析示例**:
```ruby
# 输入: "最近一个月的素材"
# 解析结果:
{
  created_at_gteq: 30.days.ago
}

# 输入: "已发布的素材"  
# 解析结果:
{
  state_eq: "published"
}
```

### 4. Artifact创建阶段

**关键文件**:
- `rails_bot/app/models/bot/activity_list_artifact.rb`
- `rails_bot/app/services/bot/tool.rb`

**创建流程**:
```ruby
# 1. 创建Artifact记录
artifact = Bot::Artifact.create!(
  type: 'Bot::ActivityListArtifact',
  conversation: conversation,
  intent_name: '素材查询',
  tool_cname: 'Bot::Tools::RansackTool',
  tool_function: 'query_records',
  function_params: { query: "最近一个月的素材" },
  tool_conf: {
    model_class: 'Serve::Activity',
    scope_chain: 'all'
  },
  meta: {
    q: { created_at_gteq: "2025-07-02T00:20:56.535+08:00" },
    total_count: 9
  }
)

# 2. 调用activate方法生成前端数据
artifact.activate
```

**Artifact数据格式**:
```json
{
  "id": 332,
  "intent_name": "素材查询",
  "tool_cname": "Bot::Tools::RansackTool",
  "tool_function": "query_records",
  "tool_conf": {
    "model_class": "Serve::Activity",
    "scope_chain": "all"
  },
  "function_params": {
    "query": "最近一个月的素材"
  },
  "meta": {
    "q": {
      "created_at_gteq": "2025-07-02T00:20:56.535+08:00"
    },
    "total_count": 9
  },
  "payload": {
    "params": {
      "q": {
        "created_at_gteq": "2025-07-02T00:20:56.535+08:00"
      }
    }
  }
}
```

### 5. 消息创建和返回

**关键文件**:
- `rails_bot/app/models/bot/message.rb`
- `rails_bot/app/views/bot/messages/_simple.json.jbuilder`

**消息格式**:
```json
{
  "role": "assistant",
  "meta": {
    "messages": [
      {
        "content_type": "text",
        "content": "在最近一个月内，我们有9个素材记录。如果您需要具体的素材列表或详细信息，请告知，我可以为您提供更精确的信息。"
      },
      {
        "content_type": "artifact",
        "content": {
          // Artifact完整数据
        }
      }
    ]
  }
}
```

### 6. 前端组件渲染

**关键文件**:
- `zj_iest/src/components/botConfig.ts` - 组件映射配置
- `zj_iest/src/components/bot/tools/ComActivityListTool.vue` - 素材列表组件

**组件映射**:
```typescript
export const BotMentionTypeMapping: VObject = {
  'Bot::ActivityListArtifact': defineAsyncComponent(
    () => import('@/components/bot/tools/ComActivityListTool.vue'),
  ),
};
```

**前端处理流程**:
1. 接收artifact数据
2. 从`props.params.payload.params.q`提取查询条件
3. 调用`ServeManageUserActivitiesApi`执行查询
4. 使用`TaIndexView`渲染素材列表
5. 支持详情弹窗、原文链接等交互

## 关键技术点

### 1. Intent识别机制
- 使用LLM进行自然语言理解
- 基于Intent描述和示例进行匹配
- 支持多种查询表达方式

### 2. 查询条件解析
- 时间范围: "最近一周/一个月/三个月/半年/一年"
- 状态过滤: "已发布/待发布/草稿/已归档"
- 关键词搜索: 标题内容匹配

### 3. 数据流转换
- LLM输出 → Ransack查询参数
- 数据库查询结果 → Artifact元数据
- Artifact数据 → 前端组件参数

### 4. 前后端协议
- 统一的JSON数据格式
- 标准化的组件映射机制
- 一致的错误处理方式

## 相关文件清单

### 后端核心文件
- `rails_bot/app/models/bot/activity_list_artifact.rb` - Artifact模型
- `rails_bot/app/services/bot/tools/ransack_tool.rb` - 查询工具
- `rails_bot/app/models/bot/intent.rb` - Intent模型
- `rails_bot/app/services/bot/assistant.rb` - Bot助手服务

### 前端核心文件  
- `zj_iest/src/components/bot/tools/ComActivityListTool.vue` - 素材列表组件
- `zj_iest/src/components/botConfig.ts` - 组件映射配置
- `zj_iest/src/engines/iest/serve-core/apis/serve/manage/user/activities.api.ts` - API接口

### 配置文件
- 数据库Intent记录 (ID: 18)
- `tool_conf`配置: `{model_class: "Serve::Activity", scope_chain: "all"}`

这个完整的流程确保了从用户自然语言输入到最终前端组件渲染的无缝体验。
