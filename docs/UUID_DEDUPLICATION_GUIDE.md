# UUID 消息去重功能使用指南

## 概述

UUID 消息去重功能通过为每条消息分配唯一的 UUID 来防止用户收到重复的消息内容，同时实现智能的模板轮换机制。

## 功能特性

- ✅ **UUID 唯一性保证**: 每条消息都有唯一的 content_uuid
- ✅ **模板轮换机制**: 优先选择与上次不同的模板
- ✅ **性能监控**: 完整的性能指标记录
- ✅ **历史数据兼容**: 支持历史数据迁移
- ✅ **配置化管理**: 多租户规则可配置

## 数据库变更

### 新增字段
```sql
-- serve_messages 表新增字段
ALTER TABLE serve_messages ADD COLUMN content_uuid VARCHAR(255) COMMENT '内容UUID，用于去重判断';

-- 新增索引
CREATE INDEX idx_serve_messages_content_uuid ON serve_messages(content_uuid);
CREATE INDEX idx_serve_messages_user_content_uuid ON serve_messages(user_id, content_uuid);
```

## 配置文件

### 多租户规则配置
文件位置: `config/multi_tenant_rules.yml`

```yaml
default: &default
  multi_tenant_rules:
    37: ['IEST', 'BID']   # 评标专家确定
    38: ['IEST', 'BID']   # 招标项目登记
    # ... 更多规则
```

## 使用方法

### 1. 数据库迁移
```bash
# 运行迁移
rails db:migrate

# 验证迁移结果
rails serve:uuid_deduplication:validate
```

### 2. 历史数据迁移
```bash
# 为历史数据补充UUID
rails serve:uuid_deduplication:migrate_historical_data

# 或者分步执行
rails serve:uuid_migration:backfill_historical_uuids
rails serve:uuid_migration:validate_historical_uuids
```

### 3. 监控和维护
```bash
# 生成监控报告
rails serve:uuid_deduplication:monitoring_report

# 清理日志文件
rails serve:uuid_deduplication:cleanup_logs

# 分析性能趋势
rails serve:uuid_deduplication:analyze_performance
```

## UUID 格式说明

### 新消息 UUID 格式
```
template_<模板ID>_<用户ID>_<hash>
例如: template_5_123_a1b2c3d4
```

### 历史数据 UUID 格式
```
historical_<消息ID>_<用户ID>_<hash>
例如: historical_1001_123_e5f6g7h8
```

## 监控和日志

### 日志文件
- 位置: `log/uuid_deduplication.log`
- 格式: `[时间] 级别 [UUID_DEDUP] 消息内容`

### 监控指标
- `template_selection_time`: 模板选择耗时
- `deduplication_query_time`: 去重查询耗时
- `template_exhaustion`: 模板耗尽次数

## 故障排除

### 常见问题

1. **UUID 重复**
   ```bash
   rails serve:uuid_migration:cleanup_duplicate_uuids
   ```

2. **性能问题**
   - 检查数据库索引是否正确创建
   - 查看监控日志中的慢查询

3. **配置加载失败**
   - 检查 `config/multi_tenant_rules.yml` 文件格式
   - 查看应用日志中的警告信息

### 验证命令
```bash
# 验证功能完整性
rails serve:uuid_deduplication:validate

# 验证历史数据
rails serve:uuid_migration:validate_historical_uuids
```

## 开发注意事项

1. **新消息创建**: 确保设置 `content_uuid` 字段
2. **查询优化**: 优先使用 `content_uuid` 字段而非 JSON 查询
3. **配置更新**: 修改多租户规则后需要重启应用
4. **监控关注**: 定期检查模板耗尽和性能指标

## API 变更

### MessageTemplate 新增方法
- `template_signature`: 生成模板签名
- `generate_unique_uuid(user, timestamp)`: 生成唯一UUID
- `content_changed?`: 检查内容是否变化

### Pack 新增方法
- `select_unique_message_template_with_uuid`: UUID方案模板选择
- `user_received_content_uuid?`: 检查用户是否收到过指定UUID消息

## 性能优化建议

1. **数据库索引**: 确保 content_uuid 相关索引存在
2. **批量处理**: 历史数据迁移使用批量处理
3. **缓存策略**: 考虑缓存多租户配置
4. **监控告警**: 设置性能阈值告警
