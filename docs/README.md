# 项目文档目录

## 📋 文档概览

本目录包含项目的核心技术文档，已整理删除重复和过时文档。

## 🚀 核心功能文档

### UUID 消息去重功能
- **`UUID_MESSAGE_DEDUPLICATION_GUIDE.md`** - UUID消息去重功能完整指南
  - 功能概述和架构设计
  - 使用方法和配置说明
  - 故障排除和性能优化
  - API变更和最佳实践

### 系统配置
- **`Rule系统配置指南.md`** - 系统规则配置文档
  - 规则系统架构说明
  - 配置参数详解
  - 使用示例和注意事项

## 🔧 集成功能文档

### 浙政钉集成
- **`dingtalk_callback_implementation_summary.md`** - 浙政钉回调功能实施总结
  - 岗位变动检测回调机制
  - 架构设计和实现方案
  - 部署和维护指南

- **`dingtalk_position_change_analysis.md`** - 浙政钉职位变更分析
  - 职位变更检测逻辑
  - 数据同步机制
  - 异常处理方案

- **`浙政钉回调问题分析与解决方案.md`** - 回调问题分析与解决
  - 常见问题和解决方案
  - CSRF Token 处理
  - 网络和权限问题排查

### 政府项目集成
- **`government_project/`** - 政府项目相关文档目录
  - `README.md` - 项目概述
  - `architecture.md` - 架构设计
  - `data_structure.md` - 数据结构
  - `quick_start.md` - 快速开始
  - `troubleshooting.md` - 故障排除

### 业务功能
- **`construction_permit_sync_fix.md`** - 施工许可同步修复
  - 同步问题分析
  - 修复方案实施
  - 验证和测试结果

- **`serve_bid_project_documentation.md`** - 招投标项目文档
  - 招投标业务流程
  - 系统集成方案
  - 数据处理逻辑

## 📊 文档维护

### 文档分类
- **核心功能**: UUID去重、系统配置等核心技术文档
- **集成功能**: 第三方系统集成相关文档
- **业务功能**: 具体业务模块的技术文档
- **项目文档**: 政府项目等特定项目文档

### 维护原则
- 保持文档的时效性和准确性
- 删除过时和重复的文档
- 统一文档格式和命名规范
- 定期审查和更新内容

### 文档更新记录
- **2025-01-30**: 整理docs目录，删除重复和过时文档
- **2025-01-30**: 添加UUID消息去重功能完整文档
- **2025-01-30**: 创建文档目录结构说明

## 📞 支持

如需了解特定功能的详细信息，请查看对应的文档文件。如有问题或建议，请联系开发团队。

---
**最后更新**: 2025-01-30
**维护者**: 开发团队
