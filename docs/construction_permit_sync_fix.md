# 施工许可证同步修复方案

## 问题描述

施工许可证同步中 `construction_permit_deal_time` 字段无法获取到时间和 `payload` 字段为空的问题。

## 问题根因分析

### 1. 同步流程缺失

- ❌ **施工许可证同步服务没有被定时任务调用**
- ✅ 立项同步正常（通过 `GovernmentProjectIntegration`）
- ✅ 项目变更同步正常（通过 `GovernmentProjectIntegration`）
- ❌ 施工许可证同步缺失（`Irs::ShigongPermitSyncService` 存在但未被调用）

### 2. 字段名不一致

- 创建项目时使用：`government_setup_deal_time`
- 读取时期望：`construction_permit_deal_time`

### 3. 数据流向问题

```
传统招投标: Biz::Zbxm → Serve::BidProject (✅ 正常)
立项数据:   政府接口 → BidProject.payload (✅ 正常)
项目变更:   政府接口 → BidProject.payload (✅ 正常)
施工许可证: 政府接口 → BidProject.payload (❌ 缺失)
```

## 修复方案

### 1. 修改定时任务调度器（保持三种同步一致性）

**文件**: `app/schedulers/serve/bid_project_scheduler.rb`

**修改内容**:

- 添加 `sync_government_projects` 方法
- **统一调用三种同步服务**：
  - `Irs::LixiangProjectSyncService.sync_projects_by_date`
  - `Irs::ShigongPermitSyncService.sync_projects_by_date`
  - `Irs::ProjectChangeSyncService.sync_projects_by_date`
- 调用政府项目时间设置方法

**新增功能**:

```ruby
def sync_government_projects
  # 同步立项项目
  sync_lixiang_projects(Date.yesterday)

  # 同步施工许可证项目
  sync_shigong_permit_projects(Date.yesterday)

  # 同步项目变更
  sync_project_changes(Date.yesterday)

  # 为现有项目设置政府项目相关时间
  sync_government_project_times(Date.yesterday)
end
```

**一致性保证**：现在三种同步都使用相同的模式：

1. 创建新项目（通过各自的同步服务）
2. 设置现有项目时间（通过 GovernmentProjectIntegration）

### 2. 修复施工许可证服务字段映射

**文件**: `app/services/irs/shigong_permit_sync_service.rb`

**修改内容**:

```ruby
def build_project_payload(irs_data)
  {
    # 施工许可证办理时间（统一使用 construction_permit_deal_time）
    construction_permit_deal_time: irs_data["deal_time"],

    # 政府审批办理时间（保持兼容性）
    government_setup_deal_time: irs_data["deal_time"],

    # ... 其他字段
  }
end
```

### 3. 执行流程

现在的完整执行流程：

```
每天凌晨5点 → Serve::BidProjectScheduler
├── 同步传统招投标项目 (Serve::BidProject.async_all!)
├── 同步施工许可证项目 (Irs::ShigongPermitSyncService)
└── 设置政府项目时间 (GovernmentProjectIntegration)
    ├── 立项时间设置
    ├── 施工许可证时间设置
    └── 项目变更时间设置
```

## 修复效果

### 1. 施工许可证项目创建

- ✅ 自动创建施工许可证项目
- ✅ 正确设置 `construction_permit_deal_time` 字段
- ✅ 完整的 `payload` 数据

### 2. 现有项目时间补充

- ✅ 为现有项目设置施工许可证办理时间
- ✅ 通过 `invest_project_code` 关联匹配
- ✅ 自动更新 `payload["construction_permit_deal_time"]`

### 3. 数据完整性

- ✅ `construction_permit_deal_time` 方法正常返回时间
- ✅ `payload` 字段包含完整的施工许可证数据
- ✅ 与立项、项目变更数据保持一致

## 测试验证

### 1. 运行测试脚本

```ruby
# 在Rails控制台中运行
load 'test_construction_permit_sync.rb'
```

### 2. 手动触发同步

```ruby
# 同步昨天的施工许可证项目
result = Irs::ShigongPermitSyncService.sync_projects_by_date(Date.yesterday)

# 手动执行定时任务
Serve::BidProjectScheduler.new.perform
```

### 3. 检查数据

```ruby
# 检查施工许可证项目
projects = Serve::BidProject.where("payload->>'shigong_permit_project' = 'true'")

# 检查时间字段
projects.each do |p|
  puts "#{p.name}: #{p.construction_permit_deal_time}"
end
```

## 监控建议

### 1. 日志监控

- 监控 `[BidProjectScheduler]` 相关日志
- 关注施工许可证同步成功/失败日志
- 检查政府接口调用状态

### 2. 数据监控

- 每日检查施工许可证项目创建数量
- 监控 `construction_permit_deal_time` 字段设置情况
- 验证 `payload` 数据完整性

### 3. 告警设置

- 施工许可证同步失败告警
- 政府接口调用异常告警
- 数据缺失告警

## 注意事项

1. **向后兼容**: 保持了 `government_setup_deal_time` 字段的兼容性
2. **错误处理**: 添加了完整的异常处理和日志记录
3. **性能考虑**: 使用缓存机制避免重复调用政府接口
4. **数据一致性**: 确保三种同步（立项、施工许可证、项目变更）的数据格式一致

## 预期结果

修复后，施工许可证相关功能将完全正常：

- ✅ `construction_permit_deal_time` 字段正确返回时间
- ✅ `payload` 字段包含完整数据
- ✅ 自动同步流程正常运行
- ✅ 与立项、项目变更功能保持一致
