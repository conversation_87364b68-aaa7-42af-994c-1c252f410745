{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T06:29:46.700Z", "updatedAt": "2025-08-01T06:29:46.708Z", "resourceCount": 12}, "resources": [{"id": "code-reviewer", "source": "project", "protocol": "role", "name": "Code Reviewer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/code-reviewer/code-reviewer.role.md", "metadata": {"createdAt": "2025-08-01T06:29:46.704Z", "updatedAt": "2025-08-01T06:29:46.704Z", "scannedAt": "2025-08-01T06:29:46.704Z", "path": "role/code-reviewer/code-reviewer.role.md"}}, {"id": "code-review-workflow", "source": "project", "protocol": "execution", "name": "Code Review Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/code-reviewer/execution/code-review-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T06:29:46.704Z", "updatedAt": "2025-08-01T06:29:46.704Z", "scannedAt": "2025-08-01T06:29:46.704Z", "path": "role/code-reviewer/execution/code-review-workflow.execution.md"}}, {"id": "analytical-thinking", "source": "project", "protocol": "thought", "name": "Analytical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/document-analyst/thought/analytical-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T06:29:46.706Z", "updatedAt": "2025-08-01T06:29:46.706Z", "scannedAt": "2025-08-01T06:29:46.706Z", "path": "role/document-analyst/thought/analytical-thinking.thought.md"}}, {"id": "quality-focused", "source": "project", "protocol": "thought", "name": "Quality Focused 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/code-reviewer/thought/quality-focused.thought.md", "metadata": {"createdAt": "2025-08-01T06:29:46.705Z", "updatedAt": "2025-08-01T06:29:46.705Z", "scannedAt": "2025-08-01T06:29:46.705Z", "path": "role/code-reviewer/thought/quality-focused.thought.md"}}, {"id": "document-analyst", "source": "project", "protocol": "role", "name": "Document Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/document-analyst/document-analyst.role.md", "metadata": {"createdAt": "2025-08-01T06:29:46.705Z", "updatedAt": "2025-08-01T06:29:46.705Z", "scannedAt": "2025-08-01T06:29:46.705Z", "path": "role/document-analyst/document-analyst.role.md"}}, {"id": "document-analysis-workflow", "source": "project", "protocol": "execution", "name": "Document Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/document-analyst/execution/document-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T06:29:46.705Z", "updatedAt": "2025-08-01T06:29:46.705Z", "scannedAt": "2025-08-01T06:29:46.705Z", "path": "role/document-analyst/execution/document-analysis-workflow.execution.md"}}, {"id": "detail-oriented", "source": "project", "protocol": "thought", "name": "Detail Oriented 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/document-analyst/thought/detail-oriented.thought.md", "metadata": {"createdAt": "2025-08-01T06:29:46.706Z", "updatedAt": "2025-08-01T06:29:46.706Z", "scannedAt": "2025-08-01T06:29:46.706Z", "path": "role/document-analyst/thought/detail-oriented.thought.md"}}, {"id": "irs-expert", "source": "project", "protocol": "role", "name": "Irs Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/irs-expert/irs-expert.role.md", "metadata": {"createdAt": "2025-08-01T06:29:46.707Z", "updatedAt": "2025-08-01T06:29:46.707Z", "scannedAt": "2025-08-01T06:29:46.707Z", "path": "role/irs-expert/irs-expert.role.md"}}, {"id": "irs-interface-analysis", "source": "project", "protocol": "thought", "name": "Irs Interface Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/irs-expert/thought/irs-interface-analysis.thought.md", "metadata": {"createdAt": "2025-08-01T06:29:46.707Z", "updatedAt": "2025-08-01T06:29:46.707Z", "scannedAt": "2025-08-01T06:29:46.707Z", "path": "role/irs-expert/thought/irs-interface-analysis.thought.md"}}, {"id": "web-content-workflow", "source": "project", "protocol": "execution", "name": "Web Content Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-doc-viewer/execution/web-content-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T06:29:46.707Z", "updatedAt": "2025-08-01T06:29:46.707Z", "scannedAt": "2025-08-01T06:29:46.707Z", "path": "role/web-doc-viewer/execution/web-content-workflow.execution.md"}}, {"id": "web-content-analysis", "source": "project", "protocol": "thought", "name": "Web Content Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/web-doc-viewer/thought/web-content-analysis.thought.md", "metadata": {"createdAt": "2025-08-01T06:29:46.708Z", "updatedAt": "2025-08-01T06:29:46.708Z", "scannedAt": "2025-08-01T06:29:46.708Z", "path": "role/web-doc-viewer/thought/web-content-analysis.thought.md"}}, {"id": "web-doc-viewer", "source": "project", "protocol": "role", "name": "Web Doc Viewer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/web-doc-viewer/web-doc-viewer.role.md", "metadata": {"createdAt": "2025-08-01T06:29:46.708Z", "updatedAt": "2025-08-01T06:29:46.708Z", "scannedAt": "2025-08-01T06:29:46.708Z", "path": "role/web-doc-viewer/web-doc-viewer.role.md"}}], "stats": {"totalResources": 12, "byProtocol": {"role": 4, "execution": 3, "thought": 5}, "bySource": {"project": 12}}}