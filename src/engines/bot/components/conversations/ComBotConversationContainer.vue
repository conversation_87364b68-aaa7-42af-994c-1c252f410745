<script lang="ts">
import { VObject } from '@/lib/vails';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';
import { computed, defineComponent, nextTick, onMounted, ref, toRefs, watch } from 'vue';
import ChatEventCenter from '../../utils/ChatEventCenter';
import useAudio from '../../utils/hooks/useAudio';
import ComBotChatInput from '../base/ComBotChatInput.vue';
import ComBotMessageCard from '../bot/messages/ComBotMessageCard.vue';

const ComBotConversationContainer = defineComponent({
  name: 'ComBotConversationContainer',
  components: {
    ComBotMessageCard,
    ComBotChatInput,
  },
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
    messages: { type: Array, default: () => [] },
    loading: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const fileUploader = ref<any>(null);
    const fileAllSettled = ref(true);
    const files = ref<VObject[]>([]);
    const botBottomAnchor = ref<any>(null);

    const { isListening, text, textMap, audioStreamClient } = useAudio(
      String(process.env.VUE_APP_AUDIO_STREAM_BASE_URL),
    );

    ChatEventCenter.on('audioPlaying', (isPlaying: boolean) => {
      if (isPlaying) {
        audioStreamClient.suspend();
      } else {
        audioStreamClient.resume();
      }
    });

    onMounted(() => {
      setTimeout(() => {
        scrollIntoView(botBottomAnchor.value);
      }, 500);
    });

    const localMessages = computed({
      get: () =>
        props.messages.sort((a: any, b: any) => {
          return a.id - b.id;
        }),
      set: value => emit('update:messages', value),
    });

    watch(
      () => [props.loading, localMessages.value.length],
      () => {
        nextTick(() => {
          scrollIntoView(botBottomAnchor.value);
        });
      },
    );

    const onConfirm = (value: string) => {
      if (!value) {
        ChatEventCenter.emit('showMessage', { type: 'warning', msg: '请输入内容' });
        return;
      }

      if (!fileAllSettled.value) {
        ChatEventCenter.emit('showMessage', { type: 'warning', msg: '文件上传中，请稍后' });
        return;
      }
      const cloneFiles = JSON.parse(JSON.stringify(files.value));
      emit('confirm', value, ...cloneFiles);
      text.value = '';
      textMap.clear();
      files.value = [];
    };

    ChatEventCenter.on('onConfirm', onConfirm);

    const onClickedArtifact = (artifact: VObject, message: VObject) => {
      emit('clickArtifact', artifact, message);
    };
    const onOpenFileUploader = () => {
      fileUploader.value?.openFile();
    };

    return {
      ...toRefs(props),
      text,
      onConfirm,
      fileUploader,
      fileAllSettled,
      files,
      localMessages,
      botBottomAnchor,
      onClickedArtifact,
      onOpenFileUploader,
      isListening,
    };
  },
});
export default ComBotConversationContainer;
</script>

<template lang="pug">
.com-bot-conversation-container.mp-12.flex.flex-col(style='height:fit-content;')
  .container__bg.rounded-lg.pt-4.h-full.flex.flex-col
    .space-y-4.overflow-x-hidden.mb-4.flex.flex-col.px-4.pt-8
      transition-group(name='list')
        ComBotMessageCard(
          v-for='message in localMessages',
          :record='message',
          :key='message.id',
          @clickArtifact='onClickedArtifact',
          @createUserMessage='onCreateUserMessage'

        )
        .bot-bottom-anchor(ref='botBottomAnchor')
    .sticky.-bottom-4
      TaFileUploader.mb-4(
        ref='fileUploader',
        v-model:isAllSettled='fileAllSettled',
        v-model:value='files',
        :multiple='false',
        class='!block',
      )
        .empty
      ComBotChatInput(
        v-model:value='text'
        v-model:isListening='isListening'
        @confirm='onConfirm'
        @file='onOpenFileUploader'
      )

</template>

<style lang="stylus" scoped>
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
