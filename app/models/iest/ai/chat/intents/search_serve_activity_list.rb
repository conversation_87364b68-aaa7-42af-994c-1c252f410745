class Iest::Ai::Chat::Intents::SearchServeActivityList < ::Chat::Intent
  def prompt_summary
    super || '你是一个素材查询助手。请根据提供描述，提取查询素材所需的条件信息。'
  end

  def keywords
    intent_keywords.present? ?
      to_keywords(
        content_type_tags: "[#{app.serve_content_type_tags.pluck(:name).join(',')}]"
      ) :
      self.class.keywords(content_type_tags: app.serve_content_type_tags.pluck(:name))
  end

  def self.keywords(content_type_tags: [])
    [
      { key: 'time_range', type: :string, desc: '时间范围，如：最近一周、最近一个月、最近三个月等' },
      { key: 'status', type: :string, desc: '素材状态，如：已发布、待发布、草稿等' },
      { key: 'keyword', type: :string, desc: '搜索关键词，用于匹配素材标题或内容' },
      { key: 'content_type', type: :array, desc: "素材类型数组。可选值有：[#{content_type_tags.join(',')}]" }
    ]
  end

  def need_mention?
    true
  end

  def answer(sentence)
    # 直接解析用户输入，不需要调用AI
    generate_answer_from_sentence(sentence)
  end

  def ai_response_to_payload(res)
    time_range = res.dig('time_range')
    status = res.dig('status')
    keyword = res.dig('keyword')
    content_type = res.dig('content_type') || []

    # 构建查询条件
    conditions = {}

    # 处理时间范围
    if time_range.present?
      case time_range
      when /最近一周|一周|7天/
        conditions[:created_at_gteq] = 7.days.ago
      when /最近一个月|一个月|30天/
        conditions[:created_at_gteq] = 30.days.ago
      when /最近三个月|三个月|90天/
        conditions[:created_at_gteq] = 90.days.ago
      when /最近半年|半年|180天/
        conditions[:created_at_gteq] = 180.days.ago
      when /最近一年|一年|365天/
        conditions[:created_at_gteq] = 1.year.ago
      end
    end

    # 处理状态
    if status.present?
      case status
      when /已发布|发布/
        conditions[:state_eq] = 'published'
      when /待发布|未发布/
        conditions[:state_eq] = 'pending'
      when /草稿/
        conditions[:state_eq] = 'draft'
      when /已归档|归档/
        conditions[:state_eq] = 'archived'
      end
    end

    # 处理关键词搜索
    if keyword.present?
      conditions[:name_cont] = keyword
    end

    # 处理内容类型
    if content_type.any?
      # 这里可以根据需要添加内容类型过滤逻辑
    end

    {
      q: conditions,
      time_range: time_range,
      status: status,
      keyword: keyword,
      content_type: content_type
    }.with_indifferent_access
  end

  def generate_answer(res)
    result = ai_response_to_payload(res)
    conditions = result[:q]

    # 执行查询获取结果
    query = ::Serve::Activity.all

    # 应用查询条件
    if conditions[:created_at_gteq]
      query = query.where('created_at >= ?', conditions[:created_at_gteq])
    end

    if conditions[:state_eq]
      query = query.where(state: conditions[:state_eq])
    end

    if conditions[:name_cont]
      query = query.where('name ILIKE ?', "%#{conditions[:name_cont]}%")
    end

    total_count = query.count

    # 构建描述
    description_parts = []
    if result[:time_range].present?
      description_parts << result[:time_range]
    end
    if result[:status].present?
      description_parts << result[:status]
    end
    if result[:keyword].present?
      description_parts << "包含「#{result[:keyword]}」"
    end

    description = description_parts.any? ? description_parts.join('的') + '的素材' : '素材'

    content = if total_count > 0
      "找到 #{total_count} 条#{description}，点击下方列表查看详情。"
    else
      "没有找到符合条件的#{description}，请尝试调整查询条件。"
    end

    {
      ai_response: res,
      result_type: 'search_serve_activity_list',
      content: content,
      mentionable_type: 'Bot::ActivityListArtifact',
      mention_type: 'Iest::Ai::Chat::Mentions::SearchServeActivityList'
    }
  end

  def generate_answer_from_sentence(sentence)
    # 直接从用户输入解析查询条件
    conditions = parse_query_conditions(sentence)

    # 执行查询获取结果
    query = ::Serve::Activity.all

    # 应用查询条件
    if conditions[:created_at_gteq]
      query = query.where('created_at >= ?', conditions[:created_at_gteq])
    end

    if conditions[:state_eq]
      query = query.where(state: conditions[:state_eq])
    end

    if conditions[:name_cont]
      query = query.where('name ILIKE ?', "%#{conditions[:name_cont]}%")
    end

    total_count = query.count

    # 构建描述
    description = build_description_from_sentence(sentence)

    content = if total_count > 0
      "找到 #{total_count} 条#{description}，点击下方列表查看详情。"
    else
      "没有找到符合条件的#{description}，请尝试调整查询条件。"
    end

    {
      ai_response: { sentence: sentence, conditions: conditions },
      result_type: 'search_serve_activity_list',
      content: content,
      mentionable_type: 'Bot::ActivityListArtifact',
      mention_type: 'Iest::Ai::Chat::Mentions::SearchServeActivityList'
    }
  end

  def parse_query_conditions(sentence)
    conditions = {}

    # 处理时间范围
    case sentence
    when /最近一周|一周|7天/
      conditions[:created_at_gteq] = 7.days.ago
    when /最近一个月|一个月|30天/
      conditions[:created_at_gteq] = 30.days.ago
    when /最近三个月|三个月|90天/
      conditions[:created_at_gteq] = 90.days.ago
    when /最近半年|半年|180天/
      conditions[:created_at_gteq] = 180.days.ago
    when /最近一年|一年|365天/
      conditions[:created_at_gteq] = 1.year.ago
    end

    # 处理状态
    case sentence
    when /已发布|发布/
      conditions[:state_eq] = 'published'
    when /待发布|未发布/
      conditions[:state_eq] = 'pending'
    when /草稿/
      conditions[:state_eq] = 'draft'
    when /已归档|归档/
      conditions[:state_eq] = 'archived'
    end

    conditions
  end

  def build_description_from_sentence(sentence)
    description_parts = []

    case sentence
    when /最近一周|一周|7天/
      description_parts << '最近一周'
    when /最近一个月|一个月|30天/
      description_parts << '最近一个月'
    when /最近三个月|三个月|90天/
      description_parts << '最近三个月'
    when /最近半年|半年|180天/
      description_parts << '最近半年'
    when /最近一年|一年|365天/
      description_parts << '最近一年'
    end

    case sentence
    when /已发布|发布/
      description_parts << '已发布'
    when /待发布|未发布/
      description_parts << '待发布'
    when /草稿/
      description_parts << '草稿'
    when /已归档|归档/
      description_parts << '已归档'
    end

    description_parts.any? ? description_parts.join('的') + '的素材' : '素材'
  end
end
