class Iest::Ai::Chat::Mentions::SearchServeActivityList < Iest::Ai::Chat::Mention
  def activate
    # 创建Bot::ActivityListArtifact
    conditions = payload[:q] || {}
    total_count = calculate_total_count(conditions)
    
    artifact = Bot::ActivityListArtifact.create!(
      type: 'Bot::ActivityListArtifact',
      conversation: nil, # Iest系统可能不需要conversation
      intent_name: '素材查询',
      tool_cname: 'Bot::Tools::RansackTool',
      tool_function: :query_records,
      function_params: { query: payload[:time_range] || payload[:status] || '素材查询' },
      tool_conf: {
        model_class: 'Serve::Activity',
        scope_chain: 'all'
      },
      meta: {
        q: conditions,
        total_count: total_count,
        query_description: build_query_description
      }
    )

    # 返回artifact的JSON表示
    artifact.as_jbuilder_json
  end

  private

  def calculate_total_count(conditions)
    query = ::Serve::Activity.all
    
    if conditions[:created_at_gteq]
      query = query.where('created_at >= ?', conditions[:created_at_gteq])
    end
    
    if conditions[:state_eq]
      query = query.where(state: conditions[:state_eq])
    end
    
    if conditions[:name_cont]
      query = query.where('name ILIKE ?', "%#{conditions[:name_cont]}%")
    end

    query.count
  end

  def build_query_description
    description_parts = []
    
    if payload[:time_range].present?
      description_parts << payload[:time_range]
    end
    
    if payload[:status].present?
      description_parts << payload[:status]
    end
    
    if payload[:keyword].present?
      description_parts << "包含「#{payload[:keyword]}」"
    end
    
    description_parts.any? ? description_parts.join("的") + "的素材" : "素材"
  end
end
