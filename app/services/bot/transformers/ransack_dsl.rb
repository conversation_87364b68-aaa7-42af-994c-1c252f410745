module Bot
  module Transformers
    class RansackDsl < Base
      attr_reader :model_class, :llm

      # @param model_class [Class] ActiveRecord model class
      # @param llm [Langchain::LLM::Base] LLM实例，默认使用OpenAI
      def initialize(model_class, llm: nil)
        @model_class = model_class
        @llm = llm || default_llm

        return if model_class.included_modules.include?(Bot::Searchable)

        raise ArgumentError, "#{model_class.name} 需要包含 Bot::Searchable 模块"
      end

      # 将自然语言描述转换为Ransack查询条件
      # @param description [String] 自然语言描述
      # @return [Hash] Ransack查询条件
      def transform(description)
        Rails.logger.info('=== RansackDsl Transform Start ===')
        Rails.logger.info("Input: #{description}")

        # 构建提示
        prompt = build_prompt(description)

        # 直接使用 llm.chat，让 Langchain 的适配器处理转换
        response = Bot::LlmFactory.chat_with_llm(
          llm,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: '0.0' # 使用较低的温度以获得更确定的结果
        )

        Rails.logger.debug("LLM Response: #{response.chat_completion}")

        # 解析查询条件
        conditions = parse_conditions(response.chat_completion)
        Rails.logger.info("Generated conditions: #{conditions.inspect}")

        # 验证查询条件并获取验证后的结果
        validated_conditions = validate_conditions!(conditions)
        Rails.logger.info("Validated conditions: #{validated_conditions.inspect}")
        Rails.logger.info('=== RansackDsl Transform End ===')

        validated_conditions
      end

      private

      def build_fields_prompt
        sections = []
        sections << "可查询的字段说明：\n"

        # 添加属性字段
        if model_class.searchable_attributes.any?
          sections << '属性字段:'
          model_class.searchable_attributes.each do |field|
            desc = model_class.searchable_field_descriptions[field]
            next unless desc.is_a?(Hash)

            field_info = []
            field_info << desc[:name] if desc[:name]
            field_info << "(#{desc[:column_type]})" if desc[:column_type]
            field_info << desc[:description] if desc[:description]

            field_info << "可选值: #{desc[:values].join('、')}" if desc[:values].present?

            # 对于时间字段，添加特殊说明
            field_info << '支持时间范围查询，可参考下方时间查询说明' if desc[:column_type] == 'datetime'

            sections << "- #{field}: #{field_info.join(' ')}"
          end
          sections << ''
        end

        # 添加关联查询
        if model_class.searchable_associations.any?
          sections << '关联查询:'
          model_class.searchable_associations.each do |assoc|
            desc = model_class.searchable_field_descriptions[assoc]
            next unless desc.is_a?(Hash)

            sections << "- #{assoc} (#{desc[:association_type]}):"
            next unless desc[:fields].is_a?(Hash)

            desc[:fields].each do |field, field_desc|
              sections << "  #{assoc}_#{field}: #{field_desc}"
            end
          end
          sections << ''
        end

        # 添加 scope 说明
        if model_class.respond_to?(:ransackable_scopes) && model_class.ransackable_scopes.any?
          sections << '可用的查询范围:'
          model_class.ransackable_scopes.each do |scope|
            desc = model_class.searchable_field_descriptions[scope]
            sections << if desc.is_a?(Hash)
                          "- #{scope}: #{desc[:description]}"
                        else
                          "- #{scope}"
                        end
          end
          sections << ''
        end

        # 添加时间查询说明
        sections << '时间字段查询说明：'
        sections << '1. 具体时间范围查询示例（将 time_field 替换为实际的时间字段名）：'
        sections << '   - 本月/这个月: { time_field_gteq: Time.current.beginning_of_month, time_field_lteq: Time.current.end_of_month }'
        sections << '   - 上个月: { time_field_gteq: Time.current.last_month.beginning_of_month, time_field_lteq: Time.current.last_month.end_of_month }'
        sections << '2. 相对时间范围查询示例：'
        sections << '   - 最近一周: { time_field_gteq: 7.days.ago }'
        sections << '   - 最近一个月: { time_field_gteq: 30.days.ago }'
        sections << '注意：'
        sections << '1. 具体时间范围（本月、上月等）必须同时使用 gteq 和 lteq'
        sections << '2. 相对时间范围（最近一周等）只使用 gteq'
        sections << ''

        sections.join("\n")
      end

      def build_prompt(description)
        <<~PROMPT
          你是一个Rails查询专家，需要帮我将自然语言转换为Ransack查询条件。
          你的任务是生成一个Ruby Hash，用于Ransack查询。

          严格要求：
          1. 只返回一个Ruby Hash，不要有任何额外的文字说明
          2. 不要加任何前缀（如"输出:"）
          3. 不要使用代码块标记(```)
          4. 返回的必须是一个有效的Ruby Hash
          5. 如果没有查询条件，返回空的Hash: {}
          6. 只能使用提供的字段和关联，不要使用任何未列出的字段

          #{build_fields_prompt}

          Ransack查询语法说明:
          1. 基本语法：field_predicate: value
             例如：name_eq: "张三"

          2. 常用查询条件(predicate):
             - eq: 等于 (等于某个值)
             - cont: 包含 (字符串包含)
             - gteq: 大于等于 (用于数字或时间)
             - lteq: 小于等于 (用于数字或时间)

          3. 组合查询：
             - 使用 g 数组组合多个条件
             - 使用 m: "and" 表示条件之间是"与"的关系
             例如：
             {
               g: [
                 { name_cont: "张" },
                 { departments_name_eq: "技术部" }
               ],
               m: "and"
             }

          示例转换：

          输入: "张三"
          { name_cont: "张三" }

          输入: "技术部的张三"
          {
            g: [
              { name_cont: "张三" },
              { departments_name_eq: "技术部" }
            ],
            m: "and"
          }

          输入: "最近一周的流程"
          { time_field_gteq: 7.days.ago }

          输入: "本月技术部的流程"
          {
            g: [
              { time_field_gteq: Time.current.beginning_of_month },
              { time_field_lteq: Time.current.end_of_month },
              { departments_name_eq: "技术部" }
            ],
            m: "and"
          }

          现在，请将以下描述转换为Ransack查询条件：
          #{description}
        PROMPT
      end

      def extract_hash_from_text(text)
        # 计算括号的深度来正确提取嵌套的Hash
        depth = 0
        start_index = nil

        text.chars.each_with_index do |char, i|
          case char
          when '{'
            depth += 1
            start_index = i if depth == 1
          when '}'
            depth -= 1
            return text[start_index..i] if depth == 0 && start_index
          end
        end

        '{}' # 如果没有找到有效的Hash结构
      end

      def parse_conditions(completion)
        # 清理和标准化输入
        code = completion.to_s
                         .gsub(/^(输出:|返回:|result:|output:)/i, '') # 移除常见的前缀
                         .gsub(/^```\w*\s*|```\s*$/m, '')            # 移除代码块标记
                         .gsub(/^#.*$/, '')                          # 移除注释
                         .strip

        # 提取第一个有效的 Hash
        code = extract_hash_from_text(code)
        Rails.logger.debug("Extracted Hash: #{code}")

        # 确保是一个hash
        unless code.start_with?('{') && code.end_with?('}')
          Rails.logger.error("Invalid Hash Format: #{code}")
          return {}
        end

        # 安全的代码执行环境
        result = nil
        begin
          result = eval(code)
        rescue SecurityError => e
          Rails.logger.error("Security Error: #{e.message}")
          return {}
        rescue StandardError => e
          Rails.logger.error("Parse Error: #{e.message}")
          return {}
        end

        # 确保结果是Hash类型
        unless result.is_a?(Hash)
          Rails.logger.error("Invalid Result Type: #{result.inspect}")
          return {}
        end

        # 递归处理时间对象
        result = process_time_values(result)
        Rails.logger.debug("Processed Result: #{result.inspect}")

        result
      end

      def process_time_values(hash)
        case hash
        when Hash
          hash.transform_values { |v| process_time_values(v) }
        when Array
          hash.map { |v| process_time_values(v) }
        when String
          if hash.include?('Time.current') || hash.include?('days.ago')
            eval(hash)
          else
            hash
          end
        else
          hash
        end
      end

      def validate_conditions!(conditions)
        # 如果是空条件，直接返回
        return conditions if conditions.empty?

        # 如果是分组条件
        if conditions.key?(:g)
          validate_condition_group!(conditions)
        else
          # 如果是简单条件
          validate_simple_condition!(conditions)
        end

        conditions
      end

      def validate_condition_group!(conditions)
        # 如果不是分组条件，直接验证简单条件
        return validate_simple_condition!(conditions) unless conditions.key?(:g)

        # 验证分组条件的格式
        unless conditions[:g].is_a?(Array)
          Rails.logger.error("Invalid group format: #{conditions.inspect}")
          return {}
        end

        # 验证组合方式
        if conditions[:m] && !%w[and or].include?(conditions[:m].to_s.downcase)
          Rails.logger.error("Invalid combinator: #{conditions[:m]}")
          return {}
        end

        # 递归验证每个子条件
        valid_conditions = conditions[:g].map do |condition|
          if condition.key?(:g)
            sub_result = validate_condition_group!(condition)
            sub_result.empty? ? nil : sub_result
          else
            validate_simple_condition!(condition)
            condition
          end
        rescue ArgumentError => e
          Rails.logger.error("Invalid condition: #{e.message}")
          nil
        end.compact

        # 如果所有子条件都无效，返回空hash
        return {} if valid_conditions.empty?

        # 保持原有的组合方式
        {
          g: valid_conditions,
          m: conditions[:m] || 'and' # 默认使用 "and"
        }
      end

      def validate_simple_condition!(condition)
        Rails.logger.debug('=== validate_simple_condition! ===')
        Rails.logger.debug("Condition: #{condition.inspect}")

        condition.each do |key, value|
          next if %i[g m].include?(key)

          # 解析 key
          Rails.logger.debug("Processing key: #{key}")

          field_parts = key.to_s.split('_')

          # 提取可能的谓词 (predicate)
          predicate = field_parts.pop
          if %w[any all].include?(predicate) #  处理 _any, _all 这种后缀
            predicate = "#{field_parts.pop}_#{predicate}"
          elsif predicate.blank? #  处理 predicate 为空的情况
            predicate = nil
          end

          Rails.logger.debug("Field parts: #{field_parts.inspect}")
          Rails.logger.debug("Predicate: #{predicate.inspect}")

          # 检查提取的 predicate 是否是 Ransack 预定义的谓词
          unless Bot::Searchable::RANSACK_PREDICATES.key?(predicate.to_sym)
            field_parts.push(predicate) # 如果不是谓词，放回 field_parts
            nil # 重置 predicate 为 nil
          end

          field_name = field_parts.join('_').to_sym
          Rails.logger.debug("Field name: #{field_name.inspect}")

          Rails.logger.debug("Validating field: #{field_name}")
          Rails.logger.debug("Searchable attributes: #{model_class.searchable_attributes.inspect}")
          Rails.logger.debug("Searchable associations: #{model_class.searchable_associations.inspect}")

          # 验证字段
          Rails.logger.debug("Checking if attribute: #{field_name} is searchable")

          # 检查 field_parts 是否为空
          raise ArgumentError, "无效的查询字段: #{field_name}" if field_parts.empty?

          # 检查是否是 attribute 或者是否以 searchable_associations 开头
          # 对于复合字段名（如created_at），需要重新组合检查
          full_field_name = field_parts.join('_').to_sym
          base_field = field_parts.first.to_sym

          # 如果字段验证通过，继续下一个字段
          if model_class.searchable_attributes.include?(full_field_name) ||
             model_class.searchable_attributes.include?(base_field) ||
             model_class.searchable_associations.include?(base_field)
            next
          end

          raise ArgumentError, "不支持的查询字段: #{field_name} (检查了: #{full_field_name}, #{base_field})"

          #
          # # 验证查询条件
          # raise ArgumentError, "不支持的查询条件: #{predicate}" unless Bot::Searchable::RANSACK_PREDICATES.key?(predicate.to_sym)
        end

        condition
      end

      def default_llm
        Bot::LlmFactory.create
      end
    end
  end
end
