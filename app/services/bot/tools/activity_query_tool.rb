module Bot
  module Tools
    class ActivityQueryTool < Bot::Tool
      attr_reader :model_class, :scope_chain, :transformer

      define_function :query_activities, description: '查询素材列表，返回前端组件数据' do
        property :query, type: 'string', description: '自然语言查询描述', required: true
      end

      def initialize(model_class: 'Serve::Activity', scope_chain: 'all', llm: nil, **options)
        super(llm: llm, **options)
        @model_class = model_class.is_a?(String) ? model_class.constantize : model_class
        @scope_chain = scope_chain
        @transformer = Bot::Transformers::RansackDsl.new(@model_class, llm: llm)
      end

      # 在类级别注册回调
      before_execute(:query_activities) do |context|
        create_artifact(
          context[:params],
          type: 'Bot::ActivityListArtifact'
        )
      end

      after_execute(:query_activities) do |result, context|
        # 从方法执行结果中获取查询条件和统计信息
        conditions = result[:conditions] || {}
        total_count = result[:total_count] || 0

        # 更新artifact的meta数据，包含前端需要的完整结构
        update_artifact(
          meta: {
            q: conditions,
            total_count: total_count,
            query_description: build_query_description(conditions),
            # 直接在meta中包含前端期望的结构
            frontend_params: {
              payload: {
                params: {
                  q: conditions
                }
              }
            }
          }
        )
      end

      def query_activities(query:)
        # 使用transformer解析查询条件
        conditions = @transformer.transform(query)

        # 构建查询scope
        scope = @model_class
        scope = scope.send(@scope_chain) if @scope_chain && @scope_chain != 'all'

        # 执行查询
        results = scope.ransack(conditions).result
        total_count = results.count

        # 构建响应消息
        query_desc = build_query_description(conditions)
        message = "在#{query_desc}范围内，我们有#{total_count}个素材记录。如果您需要具体的素材列表或详细信息，请告知，我可以为您提供更精确的信息。"

        {
          message: message,
          total_count: total_count,
          conditions: conditions
        }
      end

      private

      def build_query_description(conditions)
        descriptions = []

        # 时间范围描述
        if conditions['created_at_gteq']
          date = begin
            Date.parse(conditions['created_at_gteq'].to_s)
          rescue StandardError
            nil
          end
          if date
            days_ago = (Date.current - date).to_i
            descriptions << case days_ago
                            when 0..1
                              '今天'
                            when 2..7
                              '最近一周'
                            when 8..30
                              '最近一个月'
                            when 31..90
                              '最近三个月'
                            else
                              "#{date.strftime('%Y年%m月%d日')}以后"
                            end
          end
        end

        # 状态描述
        if conditions['state_eq']
          state_mapping = {
            'published' => '已发布',
            'pending' => '待发布',
            'draft' => '草稿',
            'archived' => '已归档'
          }
          descriptions << state_mapping[conditions['state_eq']] || conditions['state_eq']
        end

        # 关键词描述
        descriptions << "包含「#{conditions['name_cont']}」" if conditions['name_cont']

        descriptions.empty? ? '所有素材' : descriptions.join('的')
      end
    end
  end
end
